#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const glob = require('glob');

/**
 * Fix duplicate React import issues across the entire codebase
 * Removes duplicate React imports that cause compilation errors
 */

class ReactImportFixer {
    constructor() {
        this.fixedFiles = [];
        this.errors = [];
    }

    // Find all TypeScript and TSX files
    findTsFiles(dir) {
        const pattern = path.join(dir, '**/*.{ts,tsx}');
        return glob.sync(pattern, {
            ignore: [
                '**/node_modules/**',
                '**/dist/**',
                '**/build/**',
                '**/*.d.ts',
                '**/coverage/**'
            ]
        });
    }

    // Fix duplicate React imports in a file
    fixDuplicateReactImports(filePath) {
        try {
            let content = fs.readFileSync(filePath, 'utf8');
            const originalContent = content;
            let fixCount = 0;

            // Split content into lines for analysis
            const lines = content.split('\n');
            const reactImportLines = [];
            const otherLines = [];

            // Identify React import lines and other lines
            for (let i = 0; i < lines.length; i++) {
                const line = lines[i];
                if (line.match(/^\s*import\s+.*React.*from\s+['"]react['"]/) || 
                    line.match(/^\s*import\s+\*\s+as\s+React\s+from\s+['"]react['"]/)) {
                    reactImportLines.push({
                        line: line,
                        index: i,
                        type: this.getImportType(line)
                    });
                } else {
                    otherLines.push({
                        line: line,
                        index: i
                    });
                }
            }

            // If we have multiple React imports, fix them
            if (reactImportLines.length > 1) {
                console.log(`  Found ${reactImportLines.length} React imports:`);
                reactImportLines.forEach((importLine, idx) => {
                    console.log(`    ${idx + 1}. Line ${importLine.index + 1}: ${importLine.line.trim()}`);
                });

                // Choose the best import to keep
                const bestImport = this.chooseBestReactImport(reactImportLines);
                console.log(`  Keeping: ${bestImport.line.trim()}`);

                // Rebuild the file content
                const newLines = [];
                let reactImportAdded = false;

                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i];
                    
                    // Skip duplicate React imports
                    if (line.match(/^\s*import\s+.*React.*from\s+['"]react['"]/) || 
                        line.match(/^\s*import\s+\*\s+as\s+React\s+from\s+['"]react['"]/)) {
                        
                        // Add the best import only once
                        if (!reactImportAdded) {
                            newLines.push(bestImport.line);
                            reactImportAdded = true;
                        }
                        // Skip other React imports
                    } else {
                        newLines.push(line);
                    }
                }

                content = newLines.join('\n');
                fixCount = reactImportLines.length - 1; // Number of duplicates removed
            }

            // Write file if changes were made
            if (content !== originalContent) {
                fs.writeFileSync(filePath, content);
                this.fixedFiles.push(filePath);
                console.log(`✅ Fixed ${fixCount} duplicate React imports in ${path.relative(process.cwd(), filePath)}`);
                return true;
            } else {
                console.log(`ℹ️ No duplicate React imports found in ${path.relative(process.cwd(), filePath)}`);
                return false;
            }

        } catch (error) {
            console.error(`❌ Error fixing ${filePath}: ${error.message}`);
            this.errors.push({ file: filePath, error: error.message });
            return false;
        }
    }

    // Determine the type of React import
    getImportType(line) {
        if (line.includes('import * as React')) {
            return 'namespace'; // import * as React from "react"
        } else if (line.includes('import React,')) {
            return 'default_with_named'; // import React, { useState } from "react"
        } else if (line.includes('import React')) {
            return 'default'; // import React from "react"
        } else if (line.includes('import {')) {
            return 'named'; // import { useState } from "react"
        }
        return 'unknown';
    }

    // Choose the best React import to keep
    chooseBestReactImport(reactImports) {
        // Priority order:
        // 1. namespace import (import * as React)
        // 2. default with named imports (import React, { ... })
        // 3. default import (import React)
        // 4. named imports only (import { ... })

        const priorities = {
            'namespace': 1,
            'default_with_named': 2,
            'default': 3,
            'named': 4,
            'unknown': 5
        };

        return reactImports.sort((a, b) => {
            const priorityA = priorities[a.type] || 5;
            const priorityB = priorities[b.type] || 5;
            return priorityA - priorityB;
        })[0];
    }

    // Run React import fixes on all files
    async run() {
        console.log('🔧 Starting duplicate React import fixes...\n');

        const srcDir = path.join(__dirname, '..', 'src');
        const tsFiles = this.findTsFiles(srcDir);

        console.log(`📁 Found ${tsFiles.length} TypeScript files\n`);

        let fixedCount = 0;
        for (const file of tsFiles) {
            console.log(`🔍 Checking ${path.relative(process.cwd(), file)}...`);
            if (this.fixDuplicateReactImports(file)) {
                fixedCount++;
            }
            console.log(''); // Empty line for readability
        }

        // Summary
        console.log('📊 REACT IMPORT FIX SUMMARY');
        console.log('===========================');
        console.log(`✅ Files processed: ${tsFiles.length}`);
        console.log(`🔧 Files fixed: ${fixedCount}`);
        console.log(`❌ Errors: ${this.errors.length}`);

        if (this.errors.length > 0) {
            console.log('\n❌ ERRORS:');
            this.errors.forEach(error => {
                console.log(`  - ${error.file}: ${error.error}`);
            });
        }

        if (fixedCount > 0) {
            console.log('\n🎉 React import fixes completed successfully!');
            console.log('💡 Run "npm run type-check" to verify all issues are resolved.');
        } else {
            console.log('\n✨ No duplicate React imports found - code is clean!');
        }
    }
}

// Run the React import fixer
const fixer = new ReactImportFixer();
fixer.run().catch(console.error);
